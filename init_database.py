# -*- coding: utf-8 -*-
"""
数据库初始化脚本
用于创建股票信息表
"""

import mysql.connector
from config import MYSQL_CONFIG


def create_stock_info_table():
    """创建股票信息表"""
    
    # DDL语句
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS `stock_info` (
      `stock_code` varchar(10) NOT NULL COMMENT '股票id',
      `short_name` varchar(50) NOT NULL COMMENT '股票名称',
      `exchange` varchar(10) NOT NULL COMMENT '交易所',
      `list_date` varchar(50) DEFAULT NULL,
      PRIMARY KEY (`stock_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    """
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = connection.cursor()
        
        # 执行创建表语句
        cursor.execute(create_table_sql)
        connection.commit()
        
        print("股票信息表创建成功或已存在")
        
        cursor.close()
        connection.close()
        
    except mysql.connector.Error as err:
        print(f"创建表失败: {err}")


def main():
    """主函数"""
    print("开始初始化数据库...")
    create_stock_info_table()
    print("数据库初始化完成")


if __name__ == "__main__":
    main()
