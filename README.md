# 股票信息数据采集系统

这个项目用于从adata获取股票信息并保存到MySQL数据库中。

## 文件说明

- `config.py`: 配置文件，包含MySQL连接信息和其他配置
- `main.py`: 主程序，获取股票信息并保存到数据库
- `init_database.py`: 数据库初始化脚本，创建股票信息表
- `requirements.txt`: Python依赖包列表

## 安装依赖

```bash
pip install -r requirements.txt
```

## 数据库配置

在 `config.py` 中配置MySQL连接信息：

```python
MYSQL_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'root',
    'database': 'dataservice',
    'charset': 'utf8mb4',
    'port': 3306
}
```

## 使用步骤

1. 确保MySQL服务已启动，并创建了 `dataservice` 数据库
2. 安装依赖包：`pip install -r requirements.txt`
3. 初始化数据库表：`python init_database.py`
4. 运行主程序：`python main.py`

## 数据库表结构

```sql
CREATE TABLE `stock_info` (
  `stock_code` varchar(10) NOT NULL COMMENT '股票id',
  `short_name` varchar(50) NOT NULL COMMENT '股票名称',
  `exchange` varchar(10) NOT NULL COMMENT '交易所',
  `list_date` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`stock_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## 功能特性

- 使用连接池管理数据库连接
- 支持批量插入数据
- 使用 ON DUPLICATE KEY UPDATE 处理重复数据
- 完整的错误处理和日志输出
