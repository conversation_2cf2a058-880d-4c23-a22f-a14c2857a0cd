import adata
import mysql.connector
from mysql.connector import pooling
import pandas as pd
from config import MYSQL_CONFIG, STOCK_INFO_TABLE, STOCK_INFO_FIELDS, DB_POOL_CONFIG


def create_connection_pool():
    """创建MySQL连接池"""
    try:
        pool = pooling.MySQLConnectionPool(
            pool_name=DB_POOL_CONFIG['pool_name'],
            pool_size=DB_POOL_CONFIG['pool_size'],
            pool_reset_session=DB_POOL_CONFIG['pool_reset_session'],
            **MYSQL_CONFIG
        )
        print("MySQL连接池创建成功")
        return pool
    except mysql.connector.Error as err:
        print(f"创建连接池失败: {err}")
        return None


def save_stock_info_to_mysql(df, connection_pool):
    """将股票信息保存到MySQL数据库"""
    if connection_pool is None:
        print("连接池不可用")
        return False

    try:
        # 从连接池获取连接
        connection = connection_pool.get_connection()
        cursor = connection.cursor()

        # 准备插入语句 (使用ON DUPLICATE KEY UPDATE处理重复数据)
        insert_query = f"""
        INSERT INTO {STOCK_INFO_TABLE} (stock_code, short_name, exchange, list_date)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        short_name = VALUES(short_name),
        exchange = VALUES(exchange),
        list_date = VALUES(list_date)
        """

        # 准备数据
        data_to_insert = []
        for _, row in df.iterrows():
            # 根据实际DataFrame的列名映射到数据库字段
            # 需要根据adata返回的实际列名进行映射
            # 常见的列名可能是: stock_code, name, market, list_date 等
            stock_code = row.get('stock_code', row.get('code', ''))
            short_name = row.get('short_name', row.get('name', ''))
            exchange = row.get('exchange', row.get('market', ''))
            list_date = row.get('list_date', row.get('listing_date', ''))

            data_to_insert.append((
                stock_code,
                short_name,
                exchange,
                str(list_date) if list_date else ''
            ))

        # 批量插入数据
        cursor.executemany(insert_query, data_to_insert)
        connection.commit()

        print(f"成功保存 {len(data_to_insert)} 条股票信息到数据库")

        cursor.close()
        connection.close()
        return True

    except mysql.connector.Error as err:
        print(f"保存数据到MySQL失败: {err}")
        return False


def main():
    """主函数"""
    print("开始获取股票信息...")

    # 获取股票信息
    res_df = adata.stock.info.all_code()
    print(f"获取到 {len(res_df)} 条股票信息")
    print("股票信息预览:")
    print(res_df.head())
    print("\n数据列名:", res_df.columns.tolist())

    # 创建数据库连接池
    pool = create_connection_pool()

    if pool:
        # 保存数据到MySQL
        success = save_stock_info_to_mysql(res_df, pool)
        if success:
            print("股票信息已成功保存到MySQL数据库")
        else:
            print("保存股票信息到数据库失败")
    else:
        print("无法连接到数据库")


if __name__ == "__main__":
    main()
