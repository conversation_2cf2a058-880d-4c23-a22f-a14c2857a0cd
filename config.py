# -*- coding: utf-8 -*-
"""
配置文件
包含数据库连接信息和其他配置项
"""

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'root',
    'database': 'dataservice',
    'charset': 'utf8mb4',
    'port': 3306
}

# 股票信息表配置
STOCK_INFO_TABLE = 'stock_info'

# 股票信息字段映射
STOCK_INFO_FIELDS = ['stock_code', 'short_name', 'exchange', 'list_date']

# 数据库连接池配置
DB_POOL_CONFIG = {
    'pool_name': 'stock_pool',
    'pool_size': 5,
    'pool_reset_session': True,
    'autocommit': True
}
